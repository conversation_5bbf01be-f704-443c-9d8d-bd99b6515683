import torch
import coremltools as ct
import numpy as np
from basicsr.archs.rrdbnet_arch import RRDBNet
import os

def convert_realesrgan_to_coreml():
    """
    将RealESRGAN_x4plus模型转换为CoreML格式
    """
    
    # 1. 创建模型架构
    model = RRDBNet(
        num_in_ch=3,      # 输入通道数（RGB）
        num_out_ch=3,     # 输出通道数（RGB）
        num_feat=64,      # 特征数
        num_block=23,     # RRDB块数量
        num_grow_ch=32,   # 增长通道数
        scale=4           # 放大倍数
    )
    
    # 2. 加载预训练权重
    model_path = "weights/RealESRGAN_x4plus.pth"  # 确保路径正确
    if not os.path.exists(model_path):
        print(f"模型文件不存在: {model_path}")
        print("请确保RealESRGAN_x4plus.pth文件在weights目录下")
        return
    
    # 加载权重
    checkpoint = torch.load(model_path, map_location='cpu')
    
    # 处理权重键名（如果需要）
    if 'params_ema' in checkpoint:
        state_dict = checkpoint['params_ema']
    elif 'params' in checkpoint:
        state_dict = checkpoint['params']
    else:
        state_dict = checkpoint
    
    model.load_state_dict(state_dict)
    model.eval()
    
    # 3. 创建示例输入（重要：这决定了模型的输入尺寸）
    # 使用较小的输入尺寸以适应移动设备内存限制
    example_input = torch.randn(1, 3, 256, 256)  # 批次大小=1, RGB=3, 高=256, 宽=256
    
    print("开始转换模型...")
    
    # 4. 转换为CoreML
    try:
        # 使用torch.jit.trace追踪模型
        traced_model = torch.jit.trace(model, example_input)
        
        # 转换为CoreML
        coreml_model = ct.convert(
            traced_model,
            inputs=[ct.TensorType(
                name="input_image",
                shape=example_input.shape,
                dtype=np.float32
            )],
            outputs=[ct.TensorType(
                name="output_image",
                dtype=np.float32
            )],
            minimum_deployment_target=ct.target.iOS15,  # 支持iOS 15+
            compute_units=ct.ComputeUnit.ALL  # 使用所有可用计算单元（CPU+GPU+Neural Engine）
        )
        
        # 5. 设置模型元数据
        coreml_model.short_description = "RealESRGAN x4 Super Resolution Model"
        coreml_model.author = "Real-ESRGAN Team"
        coreml_model.license = "Apache License 2.0"
        coreml_model.version = "1.0"
        
        # 设置输入输出描述
        coreml_model.input_description["input_image"] = "Input RGB image (normalized to 0-1)"
        coreml_model.output_description["output_image"] = "Super-resolved RGB image (4x resolution)"
        
        # 6. 保存模型
        output_path = "Origin.mlpackage"
        coreml_model.save(output_path)
        
        print(f"✅ 模型转换成功！")
        print(f"📁 输出文件: {output_path}")
        print(f"📊 输入尺寸: {example_input.shape}")
        print(f"💾 文件大小: {os.path.getsize(output_path) / (1024*1024):.1f} MB")
        
        
        return True
        
    except Exception as e:
        print(f"❌ 转换失败: {str(e)}")
        return False

def create_optimized_model():
    """
    创建针对移动设备优化的模型版本
    """
    print("\n🚀 创建移动设备优化版本...")
    
    # 创建更小的模型（减少块数量以降低内存使用）
    model = RRDBNet(
        num_in_ch=3,
        num_out_ch=3,
        num_feat=64,
        num_block=16,  # 减少块数量
        num_grow_ch=32,
        scale=4
    )
    
    # 加载原始权重并适配
    original_path = "weights/RealESRGAN_x4plus.pth"
    if os.path.exists(original_path):
        checkpoint = torch.load(original_path, map_location='cpu')
        
        if 'params_ema' in checkpoint:
            state_dict = checkpoint['params_ema']
        elif 'params' in checkpoint:
            state_dict = checkpoint['params']
        else:
            state_dict = checkpoint
        
        # 只加载匹配的权重
        model_dict = model.state_dict()
        filtered_dict = {k: v for k, v in state_dict.items() if k in model_dict and model_dict[k].shape == v.shape}
        model_dict.update(filtered_dict)
        model.load_state_dict(model_dict)
        
        model.eval()
        
        # 使用更小的输入尺寸
        example_input = torch.randn(1, 3, 128, 128)
        
        try:
            traced_model = torch.jit.trace(model, example_input)
            
            coreml_model = ct.convert(
                traced_model,
                inputs=[ct.TensorType(
                    name="input_image",
                    shape=example_input.shape,
                    dtype=np.float32
                )],
                minimum_deployment_target=ct.target.iOS15,
                compute_units=ct.ComputeUnit.ALL
            )
            
            output_path = "RealESRGAN_x4plus_mobile.mlpackage"
            coreml_model.save(output_path)
            
            print(f"✅ 移动优化版本创建成功: {output_path}")
            print(f"💾 文件大小: {os.path.getsize(output_path) / (1024*1024):.1f} MB")
            
        except Exception as e:
            print(f"❌ 移动版本创建失败: {str(e)}")

if __name__ == "__main__":
    print("🔄 开始RealESRGAN模型转换...")
    
    # 检查PyTorch版本
    print(f"PyTorch版本: {torch.__version__}")
    print(f"CoreMLTools版本: {ct.__version__}")
    
    # 转换标准模型
    success = convert_realesrgan_to_coreml()
    
    if success:
        # 创建移动优化版本
        create_optimized_model()
        
        print("\n📋 转换完成！生成的文件:")
        print("1. RealESRGAN_x4plus.mlmodel - 标准版本")
        print("2. RealESRGAN_x4plus_mobile.mlmodel - 移动优化版本")
        print("\n📱 建议在iOS应用中使用移动优化版本以获得更好的性能")
    else:
        print("\n❌ 转换失败，请检查错误信息并重试")