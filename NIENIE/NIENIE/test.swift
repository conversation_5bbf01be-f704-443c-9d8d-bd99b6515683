import SwiftUI
import CoreML
import Vision
import CoreImage
import CoreImage.CIFilterBuiltins
import PhotosUI

// MARK: - 超分辨率配置
struct SuperResolutionConfig {
    var outscale: Float = 4.0 // 放大倍数（1.0-8.0）
    var tileSize: Int = 256 // 瓦片大小（处理大图时分块）
    var enableGPU: Bool = true // 是否启用GPU加速
    var outputFormat: ImageFormat = .png // 输出格式
}

// MARK: - 图像格式枚举
enum ImageFormat: String, CaseIterable, Identifiable {
    case png = "PNG"
    case jpeg = "JPEG"
    var id: Self { self }
}

// MARK: - 处理状态枚举
enum ProcessingState: Equatable {
    case idle
    case loading
    case processing
    case completed
    case error(String)

    static func == (lhs: ProcessingState, rhs: ProcessingState) -> Bool {
        switch (lhs, rhs) {
        case (.idle, .idle), (.loading, .loading), (.processing, .processing), (.completed, .completed):
            return true
        case (.error(let lhsMessage), .error(let rhsMessage)):
            return lhsMessage == rhsMessage
        default:
            return false
        }
    }
}

// MARK: - 主视图
struct SuperResolutionView: View {
    @Environment(\.presentationMode) var presentationMode
    @EnvironmentObject var userState: UserState

    // 图像相关状态
    @State private var selectedImage: UIImage?
    @State private var originalImage: UIImage?
    @State private var processedImage: UIImage?
    @State private var showImagePicker = false
    @State private var showAlert = false
    @State private var alertMessage = ""
    @State private var beforeAfterPosition: CGFloat = 0.5

    // 处理状态
    @State private var processingState: ProcessingState = .idle
    @State private var processingProgress: Float = 0.0

    // 超分辨率配置
    @State private var config = SuperResolutionConfig()

    // CoreML模型
    @State private var mlModel: MLModel?

    // UI控制
    @State private var selectedControlTab: ControlTab = .basic
    @State private var showSaveOptions = false

    enum ControlTab: String, CaseIterable, Identifiable {
        case basic = "基础设置"
        case advanced = "高级选项"
        case output = "输出设置"
        var id: Self { self }
    }

    var body: some View {
        NavigationView {
            ZStack {
                Color.white.ignoresSafeArea()

                VStack(spacing: 0) {
                    headerView

                    if selectedImage == nil {
                        emptyStateView
                    } else {
                        ScrollView(.vertical, showsIndicators: false) {
                            VStack(spacing: 20) {
                                imageDisplayView
                                    .padding(.horizontal, 20)

                                controlPanelView
                                    .padding(.bottom, 100) // 给底部留出空间
                            }
                        }
                    }
                }
            }
        }
        .navigationBarHidden(true)
        .sheet(isPresented: $showImagePicker) {
            ImagePicker(selectedImage: $selectedImage)
        }
        .alert(isPresented: $showAlert) {
            Alert(title: Text("提示"), message: Text(alertMessage), dismissButton: .default(Text("确定")))
        }
        .onAppear {
            loadMLModel()
        }
        .onChange(of: selectedImage) { _, newImage in
            if let image = newImage {
                originalImage = image
                processedImage = nil
                processingState = .idle
            }
        }
    }

    // MARK: - 子视图
    private var headerView: some View {
        HStack {
            Button(action: {
                presentationMode.wrappedValue.dismiss()
            }) {
                Image(systemName: "arrow.left")
                    .font(.system(size: 18))
                    .foregroundColor(.black)
            }

            Spacer()

            Text("AI超分辨率")
                .font(Font.custom("PingFang SC", size: 20).weight(.bold))
                .foregroundColor(.black)

            Spacer()

            Button(action: {
                showImagePicker = true
            }) {
                HStack(spacing: 4) {
                    Image(systemName: "photo")
                        .font(.system(size: 16))
                    Text("选择图片")
                        .font(Font.custom("PingFang SC", size: 14).weight(.medium))
                }
                .foregroundColor(.white)
                .padding(.horizontal, 12)
                .padding(.vertical, 6)
                .background(Color.blue)
                .cornerRadius(8)
            }
        }
        .padding(.horizontal, 16)
        .padding(.top, 16)
        .padding(.bottom, 20)
    }

    private var emptyStateView: some View {
        VStack(spacing: 20) {
            Spacer()

            Image(systemName: "arrow.up.right.square")
                .font(.system(size: 60))
                .foregroundColor(.blue.opacity(0.7))

            Text("选择图片开始超分辨率处理")
                .font(Font.custom("PingFang SC", size: 18))
                .foregroundColor(.gray)

            Text("AI将图片分辨率提升至4倍，让图像更清晰")
                .font(Font.custom("PingFang SC", size: 14))
                .foregroundColor(.gray.opacity(0.7))
                .multilineTextAlignment(.center)
                .padding(.horizontal, 40)

            Button(action: {
                showImagePicker = true
            }) {
                HStack(spacing: 8) {
                    Image(systemName: "photo.fill")
                    Text("选择图片")
                        .font(Font.custom("PingFang SC", size: 16).weight(.medium))
                }
                .foregroundColor(.white)
                .padding(.horizontal, 20)
                .padding(.vertical, 12)
                .background(
                    LinearGradient(
                        colors: [Color.blue, Color.purple],
                        startPoint: .leading,
                        endPoint: .trailing
                    )
                )
                .cornerRadius(10)
                .shadow(color: Color.blue.opacity(0.3), radius: 5, x: 0, y: 2)
            }

            // 功能说明
            VStack(spacing: 8) {
                Text("支持功能：")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(.gray)

                HStack(spacing: 20) {
                    FeatureItem(icon: "arrow.up.right.square.fill", text: "4倍超分")
                    FeatureItem(icon: "speedometer", text: "快速处理")
                    FeatureItem(icon: "cpu", text: "AI增强")
                }
            }
            .padding(.top, 20)

            Spacer()
        }
    }

    private func FeatureItem(icon: String, text: String) -> some View {
        VStack(spacing: 4) {
            Image(systemName: icon)
                .font(.system(size: 20))
                .foregroundColor(.blue)
            Text(text)
                .font(.system(size: 12))
                .foregroundColor(.gray)
        }
    }

    private var imageDisplayView: some View {
        ZStack {
            Color.black.opacity(0.05)
                .cornerRadius(12)

            if let originalImg = originalImage {
                if let processed = processedImage {
                    BeforeAfterSlider(
                        beforeImage: originalImg,
                        afterImage: processed,
                        sliderPosition: $beforeAfterPosition
                    )
                    .cornerRadius(12)
                } else {
                    Image(uiImage: originalImg)
                        .resizable()
                        .scaledToFit()
                        .cornerRadius(12)
                }
            }

            // 处理状态覆盖层
            if case .processing = processingState {
                VStack(spacing: 15) {
                    ProgressView(value: processingProgress)
                        .progressViewStyle(LinearProgressViewStyle(tint: .blue))
                        .frame(width: 200)

                    Text("AI超分处理中...")
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.blue)

                    Text("\(Int(processingProgress * 100))%")
                        .font(.system(size: 14))
                        .foregroundColor(.gray)
                }
                .padding(20)
                .background(Color.white.opacity(0.95))
                .cornerRadius(12)
                .shadow(radius: 10)
            } else if case .loading = processingState {
                VStack(spacing: 10) {
                    ProgressView()
                        .scaleEffect(1.5)
                    Text("准备处理...")
                        .font(.system(size: 16))
                        .foregroundColor(.gray)
                }
                .padding(20)
                .background(Color.white.opacity(0.95))
                .cornerRadius(12)
            }
        }
        .frame(height: 400)
        .clipped()
    }

    private var controlPanelView: some View {
        VStack(spacing: 15) {
            // 控制标签页
            Picker("控制面板", selection: $selectedControlTab) {
                ForEach(ControlTab.allCases) { tab in
                    Text(tab.rawValue)
                }
            }
            .pickerStyle(.segmented)
            .padding(.horizontal, 20)

            // 控制内容
            ZStack {
                basicControlsView
                    .opacity(selectedControlTab == .basic ? 1 : 0)
                    .allowsHitTesting(selectedControlTab == .basic)

                advancedControlsView
                    .opacity(selectedControlTab == .advanced ? 1 : 0)
                    .allowsHitTesting(selectedControlTab == .advanced)

                outputControlsView
                    .opacity(selectedControlTab == .output ? 1 : 0)
                    .allowsHitTesting(selectedControlTab == .output)
            }
            .disabled(processingState == .processing || processingState == .loading)
            .opacity(processingState == .processing || processingState == .loading ? 0.5 : 1.0)

            // 处理按钮
            if originalImage != nil {
                Button(action: {
                    startSuperResolution()
                }) {
                    HStack {
                        Image(systemName: "arrow.up.right.square.fill")
                        Text(processingState == .processing ? "处理中..." : "开始超分处理")
                            .font(Font.custom("PingFang SC", size: 16).weight(.medium))
                    }
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 12)
                    .background(
                        processingState == .processing ?
                        AnyView(Color.gray) :
                        AnyView(LinearGradient(colors: [Color.blue, Color.purple], startPoint: .leading, endPoint: .trailing))
                    )
                    .cornerRadius(10)
                    .shadow(color: Color.blue.opacity(0.3), radius: 5, x: 0, y: 2)
                }
                .disabled(processingState == .processing || processingState == .loading)
                .padding(.horizontal, 20)
            }

            // 保存按钮
            if processedImage != nil {
                Button(action: {
                    showSaveOptions = true
                }) {
                    HStack {
                        Image(systemName: "square.and.arrow.down.fill")
                        Text("保存超分图片")
                            .font(Font.custom("PingFang SC", size: 16).weight(.medium))
                    }
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 12)
                    .background(Color.green)
                    .cornerRadius(10)
                }
                .padding(.horizontal, 20)
            }
        }
        .padding(.vertical, 20)
        .background(Color.gray.opacity(0.05))
        .cornerRadius(15)
        .animation(.easeInOut(duration: 0.2), value: processingState)
    }


    // MARK: - 基础控制视图
    private var basicControlsView: some View {
        VStack(spacing: 20) {
            VStack(spacing: 10) {
                Text("超分辨率设置")
                    .font(Font.custom("PingFang SC", size: 16).weight(.medium))
                    .foregroundColor(.black)
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .padding(.horizontal, 20)

                Text("调整图像放大倍数和处理参数")
                    .font(.system(size: 12))
                    .foregroundColor(.gray)
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .padding(.horizontal, 20)
            }

            // 放大倍数控制
            VStack(spacing: 15) {
                Text("放大倍数")
                    .font(.system(size: 14).weight(.medium))
                    .foregroundColor(.black)
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .padding(.horizontal, 20)

                // 预设倍数按钮
                HStack(spacing: 12) {
                    ForEach([2.0, 3.0, 4.0, 6.0, 8.0], id: \.self) { scale in
                        Button(action: {
                            config.outscale = Float(scale)
                        }) {
                            Text("\(Int(scale))x")
                                .font(.system(size: 14, weight: .medium))
                                .foregroundColor(config.outscale == Float(scale) ? .white : .blue)
                                .padding(.horizontal, 12)
                                .padding(.vertical, 6)
                                .background(config.outscale == Float(scale) ? Color.blue : Color.blue.opacity(0.1))
                                .cornerRadius(8)
                        }
                    }
                }
                .padding(.horizontal, 20)

                // 自定义倍数滑块
                SuperResolutionSlider(
                    title: "自定义倍数",
                    value: Binding(
                        get: { CGFloat(config.outscale) },
                        set: { config.outscale = Float($0) }
                    ),
                    range: 1.0...8.0,
                    step: 0.1,
                    unit: "x"
                )
            }

            // 图像信息显示
            if let originalImg = originalImage {
                VStack(spacing: 8) {
                    Text("图像信息")
                        .font(.system(size: 14).weight(.medium))
                        .foregroundColor(.black)
                        .frame(maxWidth: .infinity, alignment: .leading)
                        .padding(.horizontal, 20)

                    HStack {
                        VStack(alignment: .leading, spacing: 4) {
                            Text("原始尺寸")
                                .font(.system(size: 12))
                                .foregroundColor(.gray)
                            Text("\(Int(originalImg.size.width)) × \(Int(originalImg.size.height))")
                                .font(.system(size: 14, weight: .medium))
                                .foregroundColor(.black)
                        }

                        Spacer()

                        Image(systemName: "arrow.right")
                            .foregroundColor(.blue)

                        Spacer()

                        VStack(alignment: .trailing, spacing: 4) {
                            Text("输出尺寸")
                                .font(.system(size: 12))
                                .foregroundColor(.gray)
                            Text("\(Int(originalImg.size.width * CGFloat(config.outscale))) × \(Int(originalImg.size.height * CGFloat(config.outscale)))")
                                .font(.system(size: 14, weight: .medium))
                                .foregroundColor(.blue)
                        }
                    }
                    .padding(.horizontal, 20)
                }
                .padding(.vertical, 10)
                .background(Color.blue.opacity(0.05))
                .cornerRadius(8)
                .padding(.horizontal, 20)
            }
        }
    }



    // MARK: - 高级控制视图
    private var advancedControlsView: some View {
        VStack(spacing: 20) {
            VStack(spacing: 10) {
                Text("高级设置")
                    .font(Font.custom("PingFang SC", size: 16).weight(.medium))
                    .foregroundColor(.black)
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .padding(.horizontal, 20)

                Text("调整处理性能和质量参数")
                    .font(.system(size: 12))
                    .foregroundColor(.gray)
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .padding(.horizontal, 20)
            }

            // 处理设置
            VStack(spacing: 15) {
                Text("处理设置")
                    .font(.system(size: 14).weight(.medium))
                    .foregroundColor(.black)
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .padding(.horizontal, 20)

                // 瓦片大小设置
                VStack(spacing: 8) {
                    HStack {
                        Text("瓦片大小")
                            .font(.system(size: 13))
                            .foregroundColor(.black)
                        Spacer()
                        Text("\(config.tileSize)px")
                            .font(.system(size: 13, weight: .medium))
                            .foregroundColor(.blue)
                    }
                    .padding(.horizontal, 20)

                    HStack(spacing: 12) {
                        ForEach([128, 256, 512], id: \.self) { size in
                            Button(action: {
                                config.tileSize = size
                            }) {
                                Text("\(size)")
                                    .font(.system(size: 12, weight: .medium))
                                    .foregroundColor(config.tileSize == size ? .white : .blue)
                                    .padding(.horizontal, 12)
                                    .padding(.vertical, 6)
                                    .background(config.tileSize == size ? Color.blue : Color.blue.opacity(0.1))
                                    .cornerRadius(6)
                            }
                        }

                        Spacer()

                        Text("较小瓦片占用内存更少")
                            .font(.system(size: 10))
                            .foregroundColor(.gray)
                    }
                    .padding(.horizontal, 20)
                }

                // GPU加速开关
                HStack {
                    VStack(alignment: .leading, spacing: 4) {
                        Text("GPU加速")
                            .font(.system(size: 13))
                            .foregroundColor(.black)
                        Text("使用Neural Engine和GPU加速处理")
                            .font(.system(size: 10))
                            .foregroundColor(.gray)
                    }

                    Spacer()

                    Toggle("", isOn: $config.enableGPU)
                        .toggleStyle(SwitchToggleStyle(tint: .blue))
                }
                .padding(.horizontal, 20)
            }
        }
    }

    // MARK: - 输出控制视图
    private var outputControlsView: some View {
        VStack(spacing: 20) {
            VStack(spacing: 10) {
                Text("输出设置")
                    .font(Font.custom("PingFang SC", size: 16).weight(.medium))
                    .foregroundColor(.black)
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .padding(.horizontal, 20)

                Text("设置保存格式和质量参数")
                    .font(.system(size: 12))
                    .foregroundColor(.gray)
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .padding(.horizontal, 20)
            }

            // 输出格式选择
            VStack(spacing: 15) {
                Text("输出格式")
                    .font(.system(size: 14).weight(.medium))
                    .foregroundColor(.black)
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .padding(.horizontal, 20)

                HStack(spacing: 12) {
                    ForEach(ImageFormat.allCases) { format in
                        Button(action: {
                            config.outputFormat = format
                        }) {
                            VStack(spacing: 4) {
                                Text(format.rawValue)
                                    .font(.system(size: 14, weight: .medium))
                                Text(format == .png ? "无损压缩" : "有损压缩")
                                    .font(.system(size: 10))
                            }
                            .foregroundColor(config.outputFormat == format ? .white : .blue)
                            .padding(.horizontal, 16)
                            .padding(.vertical, 8)
                            .background(config.outputFormat == format ? Color.blue : Color.blue.opacity(0.1))
                            .cornerRadius(8)
                        }
                    }
                    Spacer()
                }
                .padding(.horizontal, 20)
            }

            // 处理结果预览
            if let processedImg = processedImage {
                VStack(spacing: 8) {
                    Text("处理结果")
                        .font(.system(size: 14).weight(.medium))
                        .foregroundColor(.black)
                        .frame(maxWidth: .infinity, alignment: .leading)
                        .padding(.horizontal, 20)

                    HStack {
                        VStack(alignment: .leading, spacing: 4) {
                            Text("文件大小")
                                .font(.system(size: 12))
                                .foregroundColor(.gray)
                            Text(formatFileSize(processedImg))
                                .font(.system(size: 14, weight: .medium))
                                .foregroundColor(.black)
                        }

                        Spacer()

                        VStack(alignment: .trailing, spacing: 4) {
                            Text("处理时间")
                                .font(.system(size: 12))
                                .foregroundColor(.gray)
                            Text("已完成")
                                .font(.system(size: 14, weight: .medium))
                                .foregroundColor(.green)
                        }
                    }
                    .padding(.horizontal, 20)
                }
                .padding(.vertical, 10)
                .background(Color.green.opacity(0.05))
                .cornerRadius(8)
                .padding(.horizontal, 20)
            }
        }
    }

    // MARK: - 逻辑处理

    private func loadMLModel() {
        guard mlModel == nil else { return }

        DispatchQueue.global(qos: .userInitiated).async {
            do {
                // 加载Origin模型
                if let modelURL = Bundle.main.url(forResource: "Origin", withExtension: ".mlmodelc") {
                    self.mlModel = try MLModel(contentsOf: modelURL)
                    print("✅ 成功加载Origin超分辨率模型")

                    // 打印模型输入输出规格以便调试
                    print("📋 模型输入规格:")
                    for inputDescription in self.mlModel!.modelDescription.inputDescriptionsByName {
                        print("  - 输入名称: \(inputDescription.key)")
                        print("    类型: \(inputDescription.value.type)")
                        if let imageConstraint = inputDescription.value.imageConstraint {
                            print("    图像尺寸: \(imageConstraint.pixelsWide) x \(imageConstraint.pixelsHigh)")
                            print("    像素格式: \(imageConstraint.pixelFormatType)")
                        }
                        if let multiArrayConstraint = inputDescription.value.multiArrayConstraint {
                            print("    MultiArray形状: \(multiArrayConstraint.shape)")
                            print("    MultiArray数据类型: \(multiArrayConstraint.dataType)")
                        }
                    }

                    print("📋 模型输出规格:")
                    for outputDescription in self.mlModel!.modelDescription.outputDescriptionsByName {
                        print("  - 输出名称: \(outputDescription.key)")
                        print("    类型: \(outputDescription.value.type)")
                    }
                } else {
                    DispatchQueue.main.async {
                        self.alertMessage = "未找到Origin.mlmodelc模型文件，请确保已将模型文件添加到项目中"
                        self.showAlert = true
                    }
                    return
                }

                DispatchQueue.main.async {
                    print("🚀 模型加载完成，可以开始处理")
                }
            } catch {
                DispatchQueue.main.async {
                    self.alertMessage = "模型加载失败: \(error.localizedDescription)"
                    self.showAlert = true
                }
            }
        }
    }

    private func startSuperResolution() {
        guard let originalImg = originalImage,
              let model = mlModel else {
            alertMessage = "请先选择图片并确保模型已加载"
            showAlert = true
            return
        }

        processingState = .loading
        processingProgress = 0.0

        DispatchQueue.global(qos: .userInitiated).async {
            do {
                let result = try self.performSuperResolution(image: originalImg, model: model)

                DispatchQueue.main.async {
                    self.processedImage = result
                    self.processingState = .completed
                    self.processingProgress = 1.0
                }
            } catch {
                DispatchQueue.main.async {
                    self.processingState = .error(error.localizedDescription)
                    self.alertMessage = "超分处理失败: \(error.localizedDescription)"
                    self.showAlert = true
                }
            }
        }
    }

    private func performSuperResolution(image: UIImage, model: MLModel) throws -> UIImage {
        // 更新处理状态
        DispatchQueue.main.async {
            self.processingState = .processing
            self.processingProgress = 0.1
        }

        // 1. 预处理图像
        guard let cgImage = image.cgImage else {
            throw SuperResolutionError.invalidImage
        }

        // 根据配置调整图像大小（如果需要分块处理）
        let inputSize = min(config.tileSize, Int(min(image.size.width, image.size.height)))
        let processedImage: UIImage

        if image.size.width > CGFloat(config.tileSize) || image.size.height > CGFloat(config.tileSize) {
            // 大图分块处理
            processedImage = try processTiledImage(image: image, model: model, tileSize: config.tileSize)
        } else {
            // 小图直接处理
            processedImage = try processSingleImage(image: image, model: model)
        }

        DispatchQueue.main.async {
            self.processingProgress = 0.9
        }

        // 3. 后处理（如果需要）
        let finalImage = postProcessImage(processedImage)

        return finalImage
    }

    private func processSingleImage(image: UIImage, model: MLModel) throws -> UIImage {
        DispatchQueue.main.async {
            self.processingProgress = 0.3
        }

        // 转换为CVPixelBuffer
        print("🔄 开始转换图像为CVPixelBuffer，尺寸: \(image.size)")
        guard let pixelBuffer = image.toCVPixelBuffer() else {
            print("❌ CVPixelBuffer转换失败")
            throw SuperResolutionError.conversionFailed
        }
        print("✅ CVPixelBuffer转换成功")

        DispatchQueue.main.async {
            self.processingProgress = 0.5
        }

        // 动态获取模型的输入和输出名称
        let inputDescriptions = model.modelDescription.inputDescriptionsByName
        let outputDescriptions = model.modelDescription.outputDescriptionsByName

        guard let inputName = inputDescriptions.keys.first else {
            throw SuperResolutionError.predictionFailed
        }

        guard let outputName = outputDescriptions.keys.first else {
            throw SuperResolutionError.predictionFailed
        }

        print("🔍 使用输入名称: \(inputName)")
        print("🔍 使用输出名称: \(outputName)")

        // 创建MLFeatureProvider
        print("🔄 创建MLFeatureProvider，输入名称: \(inputName)")

        // 尝试不同的输入格式
        var input: MLFeatureProvider

        // 首先尝试使用MLMultiArray（更可靠的方式）
        do {
            guard let multiArray = image.toMLMultiArray() else {
                throw SuperResolutionError.conversionFailed
            }

            // 验证MLMultiArray的格式
            print("🔍 MLMultiArray详细信息:")
            print("  - 形状: \(multiArray.shape)")
            print("  - 数据类型: \(multiArray.dataType)")
            print("  - 步长: \(multiArray.strides)")

            // 打印一些样本值来验证数据范围
            let sampleR = multiArray[[0, 0, 0, 0] as [NSNumber]].floatValue
            let sampleG = multiArray[[0, 1, 0, 0] as [NSNumber]].floatValue
            let sampleB = multiArray[[0, 2, 0, 0] as [NSNumber]].floatValue
            print("  - 样本像素值 (0,0): R=\(sampleR), G=\(sampleG), B=\(sampleB)")

            input = try MLDictionaryFeatureProvider(dictionary: [inputName: MLFeatureValue(multiArray: multiArray)])
            print("✅ 使用MLMultiArray创建MLFeatureProvider成功")
        } catch {
            print("❌ MLMultiArray方式失败: \(error)")

            // 如果MLMultiArray失败，尝试使用CVPixelBuffer
            do {
                input = try MLDictionaryFeatureProvider(dictionary: [inputName: MLFeatureValue(pixelBuffer: pixelBuffer)])
                print("✅ 使用CVPixelBuffer创建MLFeatureProvider成功")
            } catch {
                print("❌ CVPixelBuffer方式也失败: \(error)")
                throw SuperResolutionError.conversionFailed
            }
        }

        // 执行推理
        print("🔄 开始执行模型推理...")
        let output = try model.prediction(from: input)
        print("✅ 模型推理完成")

        DispatchQueue.main.async {
            self.processingProgress = 0.8
        }

        // 获取输出
        let resultImage: UIImage

        if let outputPixelBuffer = output.featureValue(for: outputName)?.imageBufferValue {
            // 输出是CVPixelBuffer格式
            print("✅ 获取到CVPixelBuffer输出")
            guard let image = UIImage.fromCVPixelBuffer(outputPixelBuffer) else {
                throw SuperResolutionError.conversionFailed
            }
            resultImage = image
        } else if let outputMultiArray = output.featureValue(for: outputName)?.multiArrayValue {
            // 输出是MLMultiArray格式
            print("✅ 获取到MLMultiArray输出")
            guard let image = UIImage.fromMLMultiArray(outputMultiArray) else {
                throw SuperResolutionError.conversionFailed
            }
            resultImage = image
        } else {
            print("❌ 无法获取模型输出")
            throw SuperResolutionError.predictionFailed
        }

        return resultImage
    }

    private func processTiledImage(image: UIImage, model: MLModel, tileSize: Int) throws -> UIImage {
        // 分块处理大图像
        let imageSize = image.size
        let scale = image.scale
        let tileWidth = CGFloat(tileSize)
        let tileHeight = CGFloat(tileSize)

        let cols = Int(ceil(imageSize.width / tileWidth))
        let rows = Int(ceil(imageSize.height / tileHeight))

        var processedTiles: [[UIImage]] = []

        for row in 0..<rows {
            var tileRow: [UIImage] = []

            for col in 0..<cols {
                let x = CGFloat(col) * tileWidth
                let y = CGFloat(row) * tileHeight
                let width = min(tileWidth, imageSize.width - x)
                let height = min(tileHeight, imageSize.height - y)

                let tileRect = CGRect(x: x, y: y, width: width, height: height)

                if let tileImage = image.cropped(to: tileRect) {
                    let processedTile = try processSingleImage(image: tileImage, model: model)
                    tileRow.append(processedTile)
                }

                // 更新进度
                let progress = Float(row * cols + col + 1) / Float(rows * cols) * 0.7 + 0.1
                DispatchQueue.main.async {
                    self.processingProgress = progress
                }
            }

            processedTiles.append(tileRow)
        }

        // 合并瓦片
        return try mergeTiles(processedTiles)
    }

    private func mergeTiles(_ tiles: [[UIImage]]) throws -> UIImage {
        guard !tiles.isEmpty, !tiles[0].isEmpty else {
            throw SuperResolutionError.mergeFailed
        }

        let rows = tiles.count
        let cols = tiles[0].count

        // 计算最终图像尺寸
        let tileSize = tiles[0][0].size
        let finalWidth = CGFloat(cols) * tileSize.width
        let finalHeight = CGFloat(rows) * tileSize.height

        // 创建最终图像
        UIGraphicsBeginImageContextWithOptions(CGSize(width: finalWidth, height: finalHeight), false, 1.0)
        defer { UIGraphicsEndImageContext() }

        for (rowIndex, tileRow) in tiles.enumerated() {
            for (colIndex, tile) in tileRow.enumerated() {
                let x = CGFloat(colIndex) * tileSize.width
                let y = CGFloat(rowIndex) * tileSize.height
                tile.draw(at: CGPoint(x: x, y: y))
            }
        }

        guard let mergedImage = UIGraphicsGetImageFromCurrentImageContext() else {
            throw SuperResolutionError.mergeFailed
        }

        return mergedImage
    }

    private func postProcessImage(_ image: UIImage) -> UIImage {
        // 根据配置进行后处理
        return image
    }

    private func formatFileSize(_ image: UIImage) -> String {
        guard let data = image.pngData() else { return "未知" }
        let bytes = data.count

        if bytes < 1024 {
            return "\(bytes) B"
        } else if bytes < 1024 * 1024 {
            return String(format: "%.1f KB", Double(bytes) / 1024.0)
        } else {
            return String(format: "%.1f MB", Double(bytes) / (1024.0 * 1024.0))
        }
    }



    private func saveProcessedImage() {
        guard let image = processedImage else { return }

        UIImageWriteToSavedPhotosAlbum(image, nil, nil, nil)
        alertMessage = "超分辨率图片已保存到相册"
        showAlert = true
    }
}

// MARK: - 超分辨率错误类型
enum SuperResolutionError: Error, LocalizedError {
    case invalidImage
    case modelNotLoaded
    case conversionFailed
    case predictionFailed
    case mergeFailed

    var errorDescription: String? {
        switch self {
        case .invalidImage:
            return "无效的图像格式"
        case .modelNotLoaded:
            return "模型未加载"
        case .conversionFailed:
            return "图像格式转换失败"
        case .predictionFailed:
            return "AI推理失败"
        case .mergeFailed:
            return "图像合并失败"
        }
    }
}

// MARK: - UIImage扩展
extension UIImage {
    func toCVPixelBuffer() -> CVPixelBuffer? {
        // 调整图像到模型期望的尺寸
        let targetSize = CGSize(width: 256, height: 256)
        let resizedImage = self.resized(to: targetSize)

        // 创建RGB格式的CVPixelBuffer，使用32BGRA格式（CoreML常用）
        let attrs = [
            kCVPixelBufferCGImageCompatibilityKey: kCFBooleanTrue,
            kCVPixelBufferCGBitmapContextCompatibilityKey: kCFBooleanTrue,
            kCVPixelBufferIOSurfacePropertiesKey: [:],
            kCVPixelBufferMetalCompatibilityKey: kCFBooleanTrue
        ] as CFDictionary

        var pixelBuffer: CVPixelBuffer?
        let status = CVPixelBufferCreate(
            kCFAllocatorDefault,
            Int(targetSize.width),
            Int(targetSize.height),
            kCVPixelFormatType_32BGRA, // 使用BGRA格式，这是CoreML推荐的格式
            attrs,
            &pixelBuffer
        )

        guard status == kCVReturnSuccess, let buffer = pixelBuffer else {
            print("❌ CVPixelBuffer创建失败，状态码: \(status)")
            return nil
        }

        CVPixelBufferLockBaseAddress(buffer, CVPixelBufferLockFlags(rawValue: 0))
        defer { CVPixelBufferUnlockBaseAddress(buffer, CVPixelBufferLockFlags(rawValue: 0)) }

        let pixelData = CVPixelBufferGetBaseAddress(buffer)
        let rgbColorSpace = CGColorSpaceCreateDeviceRGB()

        guard let context = CGContext(
            data: pixelData,
            width: Int(targetSize.width),
            height: Int(targetSize.height),
            bitsPerComponent: 8,
            bytesPerRow: CVPixelBufferGetBytesPerRow(buffer),
            space: rgbColorSpace,
            bitmapInfo: CGImageAlphaInfo.noneSkipFirst.rawValue | CGBitmapInfo.byteOrder32Little.rawValue
        ) else {
            print("❌ CGContext创建失败")
            return nil
        }

        // 不需要翻转，保持原始方向
        UIGraphicsPushContext(context)
        resizedImage.draw(in: CGRect(x: 0, y: 0, width: targetSize.width, height: targetSize.height))
        UIGraphicsPopContext()

        print("✅ CVPixelBuffer创建成功: \(Int(targetSize.width))x\(Int(targetSize.height))，BGRA格式")
        return buffer
    }

    // 添加图像缩放方法
    func resized(to size: CGSize) -> UIImage {
        UIGraphicsBeginImageContextWithOptions(size, false, 0.0)
        defer { UIGraphicsEndImageContext() }
        draw(in: CGRect(origin: .zero, size: size))
        return UIGraphicsGetImageFromCurrentImageContext() ?? self
    }

    // 转换为MLMultiArray（模型期望的格式：1x3xHxW，float32，0-1范围，RGB通道顺序）
    func toMLMultiArray() -> MLMultiArray? {
        // 不强制调整到256x256，保持原始尺寸或根据模型要求调整
        let targetSize = CGSize(width: 256, height: 256) // 根据转换脚本，模型期望256x256
        let resizedImage = self.resized(to: targetSize)

        guard let cgImage = resizedImage.cgImage else {
            print("❌ 无法获取CGImage")
            return nil
        }

        let width = Int(targetSize.width)
        let height = Int(targetSize.height)

        // 创建MLMultiArray：形状为[1, 3, height, width]
        guard let multiArray = try? MLMultiArray(shape: [1, 3, height, width], dataType: .float32) else {
            print("❌ MLMultiArray创建失败")
            return nil
        }

        // 创建位图上下文来提取像素数据（使用RGB格式，不包含Alpha）
        let bytesPerPixel = 4 // RGBA
        let bytesPerRow = bytesPerPixel * width
        let bitsPerComponent = 8

        guard let colorSpace = CGColorSpace(name: CGColorSpace.sRGB) else {
            print("❌ 无法创建sRGB颜色空间")
            return nil
        }

        var pixelData = [UInt8](repeating: 0, count: width * height * bytesPerPixel)

        guard let context = CGContext(
            data: &pixelData,
            width: width,
            height: height,
            bitsPerComponent: bitsPerComponent,
            bytesPerRow: bytesPerRow,
            space: colorSpace,
            bitmapInfo: CGImageAlphaInfo.premultipliedLast.rawValue | CGBitmapInfo.byteOrder32Big.rawValue
        ) else {
            print("❌ CGContext创建失败")
            return nil
        }

        // 绘制图像
        context.draw(cgImage, in: CGRect(x: 0, y: 0, width: width, height: height))

        // 将像素数据转换为MLMultiArray格式
        // 格式：[batch, channel, height, width] = [1, 3, H, W]
        // 注意：原版Python使用RGB顺序，而iOS默认是RGBA
        for y in 0..<height {
            for x in 0..<width {
                let pixelIndex = (y * width + x) * bytesPerPixel

                // 提取RGBA值
                let r = pixelData[pixelIndex]     // Red
                let g = pixelData[pixelIndex + 1] // Green
                let b = pixelData[pixelIndex + 2] // Blue
                // Alpha通道忽略

                // 归一化到0-1范围（与原版Python一致）
                let rNorm = Float(r) / 255.0
                let gNorm = Float(g) / 255.0
                let bNorm = Float(b) / 255.0

                // 设置MLMultiArray的值，按照RGB通道顺序
                // 通道0: R, 通道1: G, 通道2: B
                multiArray[[0, 0, y, x] as [NSNumber]] = NSNumber(value: rNorm)
                multiArray[[0, 1, y, x] as [NSNumber]] = NSNumber(value: gNorm)
                multiArray[[0, 2, y, x] as [NSNumber]] = NSNumber(value: bNorm)
            }
        }

        print("✅ MLMultiArray创建成功: 形状[1, 3, \(height), \(width)]，RGB格式，0-1范围")
        return multiArray
    }

    // 从MLMultiArray创建UIImage
    static func fromMLMultiArray(_ multiArray: MLMultiArray) -> UIImage? {
        // 期望输出格式：[1, 3, height, width]
        guard multiArray.shape.count == 4,
              multiArray.shape[0].intValue == 1,
              multiArray.shape[1].intValue == 3 else {
            print("❌ MLMultiArray形状不正确: \(multiArray.shape)")
            return nil
        }

        let height = multiArray.shape[2].intValue
        let width = multiArray.shape[3].intValue

        print("🔄 从MLMultiArray创建图像: \(width)x\(height)")

        // 创建像素数据数组
        var pixelData = [UInt8](repeating: 0, count: width * height * 4) // RGBA

        // 从MLMultiArray提取像素值
        for y in 0..<height {
            for x in 0..<width {
                let pixelIndex = (y * width + x) * 4

                // 获取RGB值并转换到0-255范围
                let r = multiArray[[0, 0, y, x] as [NSNumber]].floatValue
                let g = multiArray[[0, 1, y, x] as [NSNumber]].floatValue
                let b = multiArray[[0, 2, y, x] as [NSNumber]].floatValue

                // 限制值在0-1范围内，然后转换为0-255
                pixelData[pixelIndex] = UInt8(max(0, min(1, r)) * 255)     // R
                pixelData[pixelIndex + 1] = UInt8(max(0, min(1, g)) * 255) // G
                pixelData[pixelIndex + 2] = UInt8(max(0, min(1, b)) * 255) // B
                pixelData[pixelIndex + 3] = 255                             // A
            }
        }

        // 创建CGImage
        guard let colorSpace = CGColorSpace(name: CGColorSpace.sRGB) else { return nil }

        let bitsPerComponent = 8
        let bytesPerPixel = 4
        let bytesPerRow = bytesPerPixel * width

        guard let context = CGContext(
            data: &pixelData,
            width: width,
            height: height,
            bitsPerComponent: bitsPerComponent,
            bytesPerRow: bytesPerRow,
            space: colorSpace,
            bitmapInfo: CGImageAlphaInfo.premultipliedLast.rawValue
        ) else {
            print("❌ CGContext创建失败")
            return nil
        }

        guard let cgImage = context.makeImage() else {
            print("❌ CGImage创建失败")
            return nil
        }

        let image = UIImage(cgImage: cgImage)
        print("✅ 从MLMultiArray创建图像成功: \(width)x\(height)")
        return image
    }

    static func fromCVPixelBuffer(_ pixelBuffer: CVPixelBuffer) -> UIImage? {
        let ciImage = CIImage(cvPixelBuffer: pixelBuffer)
        let context = CIContext()

        guard let cgImage = context.createCGImage(ciImage, from: ciImage.extent) else {
            return nil
        }

        return UIImage(cgImage: cgImage)
    }

    func cropped(to rect: CGRect) -> UIImage? {
        guard let cgImage = cgImage?.cropping(to: rect) else { return nil }
        return UIImage(cgImage: cgImage, scale: scale, orientation: imageOrientation)
    }
}

// MARK: - 超分辨率滑块组件
struct SuperResolutionSlider: View {
    let title: String
    @Binding var value: CGFloat
    let range: ClosedRange<CGFloat>
    let step: CGFloat
    let unit: String

    var body: some View {
        VStack(spacing: 8) {
            HStack {
                Text(title)
                    .font(.system(size: 13))
                    .foregroundColor(.black)
                Spacer()
                Text(String(format: "%.1f%@", value, unit))
                    .font(.system(size: 13, weight: .medium))
                    .foregroundColor(.blue)
            }
            .padding(.horizontal, 20)

            Slider(value: $value, in: range, step: step)
                .accentColor(.blue)
                .padding(.horizontal, 20)
        }
    }
}

// MARK: - 图片选择器
struct ImagePicker: UIViewControllerRepresentable {
    @Binding var selectedImage: UIImage?
    @Environment(\.presentationMode) var presentationMode

    func makeUIViewController(context: Context) -> UIImagePickerController {
        let picker = UIImagePickerController()
        picker.delegate = context.coordinator
        picker.sourceType = .photoLibrary
        return picker
    }

    func updateUIViewController(_ uiViewController: UIImagePickerController, context: Context) {}

    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }

    class Coordinator: NSObject, UINavigationControllerDelegate, UIImagePickerControllerDelegate {
        let parent: ImagePicker

        init(_ parent: ImagePicker) {
            self.parent = parent
        }

        func imagePickerController(_ picker: UIImagePickerController, didFinishPickingMediaWithInfo info: [UIImagePickerController.InfoKey : Any]) {
            if let image = info[.originalImage] as? UIImage {
                parent.selectedImage = image
            }
            parent.presentationMode.wrappedValue.dismiss()
        }

        func imagePickerControllerDidCancel(_ picker: UIImagePickerController) {
            parent.presentationMode.wrappedValue.dismiss()
        }
    }

}
